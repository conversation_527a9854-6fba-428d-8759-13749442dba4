/**
 * 临时认证修复
 * 专门用于修复权限刷新API的认证问题
 */

import { NextRequest } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import logger from '@/lib/utils/logger';

export class TempAuthFix {
  /**
   * 增强的用户认证检查
   * 专门用于权限刷新API
   */
  static async getCurrentUserForPermissionAPI(request: NextRequest): Promise<any> {
    try {
      logger.log('[权限API] 开始增强认证检查');

      // 1. 首先尝试NextAuth会话
      try {
        const session = await getServerSession(authOptions);
        logger.log('[权限API] NextAuth会话状态:', {
          exists: !!session,
          hasUser: !!session?.user,
          userId: session?.user?.id,
          roleCode: session?.user?.roleCode
        });

        if (session?.user?.id) {
          // 从数据库获取完整用户信息
          const dbUser = await prisma.user.findUnique({
            where: { id: session.user.id },
            select: {
              id: true,
              email: true,
              username: true,
              roleCode: true,
              status: true
            }
          });

          if (dbUser && dbUser.status === 'active') {
            logger.log('[权限API] 通过NextAuth获取到用户:', {
              id: dbUser.id,
              username: dbUser.username,
              roleCode: dbUser.roleCode
            });
            return dbUser;
          }
        }
      } catch (sessionError) {
        logger.error('[权限API] NextAuth会话检查失败:', sessionError);
      }

      // 2. 尝试从cookie获取令牌
      const cookieHeader = request.headers.get('cookie');
      if (cookieHeader) {
        logger.log('[权限API] 尝试从cookie获取认证信息');
        
        // 查找NextAuth会话cookie
        const cookies = cookieHeader.split(';');
        for (const cookie of cookies) {
          const [name, value] = cookie.trim().split('=');
          if (name === 'next-auth.session-token' || name === '__Secure-next-auth.session-token') {
            logger.log('[权限API] 找到NextAuth会话cookie');
            // 这里可以进一步处理会话cookie
            break;
          }
        }
      }

      // 3. 如果所有方法都失败，返回null
      logger.error('[权限API] 所有认证方法均失败');
      return null;

    } catch (error) {
      logger.error('[权限API] 认证检查过程中出错:', error);
      return null;
    }
  }

  /**
   * 检查用户是否为管理员
   */
  static isAdmin(user: any): boolean {
    if (!user || !user.roleCode) {
      return false;
    }
    
    const roleCode = user.roleCode.toUpperCase();
    return roleCode === 'ADMIN' || roleCode === 'SUPER';
  }
}
