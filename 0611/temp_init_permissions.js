/**
 * 临时权限初始化脚本
 * 用于在登录后手动初始化权限系统
 */

const { PrismaClient } = require('@prisma/client');
const { newEnforcer } = require('casbin');
const path = require('path');

const prisma = new PrismaClient();

async function initPermissions() {
  try {
    console.log('开始初始化权限系统...');

    // 初始化Casbin执行器
    const modelPath = path.resolve(process.cwd(), 'config/casbin_model.conf');
    const policyPath = path.resolve(process.cwd(), 'config/policy.csv');
    
    const enforcer = await newEnforcer(modelPath, policyPath);
    console.log('Casbin执行器初始化完成');

    // 获取所有角色及其关联的菜单和操作
    const roles = await prisma.role.findMany({
      include: {
        menus: true,
        operations: true
      }
    });

    console.log(`找到 ${roles.length} 个角色，开始同步权限...`);

    // 清除所有现有策略
    await enforcer.clearPolicy();
    console.log('已清除所有现有策略');

    // 为每个角色添加权限
    for (const role of roles) {
      console.log(`处理角色: ${role.name} (${role.code})`);

      // 如果是管理员角色，添加通配符权限
      if (role.code.toUpperCase() === 'ADMIN') {
        await enforcer.addPolicy(role.code, '*', '*');
        console.log(`已为管理员角色 ${role.code} 添加通配符权限`);
      }

      // 添加菜单权限
      for (const menu of role.menus) {
        await enforcer.addPolicy(role.code, `menu:${menu.code}`, 'view');
      }
      console.log(`已为角色 ${role.code} 添加 ${role.menus.length} 个菜单权限`);

      // 添加操作权限
      for (const operation of role.operations) {
        await enforcer.addPolicy(role.code, `operation:${operation.code}`, 'execute');
      }
      console.log(`已为角色 ${role.code} 添加 ${role.operations.length} 个操作权限`);
    }

    // 保存策略
    await enforcer.savePolicy();
    console.log('权限策略已保存');

    console.log('权限系统初始化完成');
    return true;
  } catch (error) {
    console.error('初始化权限系统失败:', error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initPermissions()
    .then(success => {
      if (success) {
        console.log('权限初始化成功');
        process.exit(0);
      } else {
        console.log('权限初始化失败');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { initPermissions };
